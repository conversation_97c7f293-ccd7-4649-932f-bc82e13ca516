from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import time
import os

def main():
    # 设置ChromeDriver路径
    driver_path = os.path.join(os.getcwd(), "driver", "chromedriver.exe")

    # 创建Chrome服务
    service = Service(driver_path)

    # 创建Chrome选项
    chrome_options = webdriver.ChromeOptions()

    # 创建WebDriver实例
    driver = webdriver.Chrome(service=service, options=chrome_options)

    try:
        # 打开网站
        print("正在打开 https://www.genspark.ai ...")
        driver.get("https://www.genspark.ai")

        print("网站已打开，开始休眠9999秒...")
        # 休眠9999秒
        time.sleep(9999)

    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭浏览器
        driver.quit()
        print("浏览器已关闭")

if __name__ == "__main__":
    main()