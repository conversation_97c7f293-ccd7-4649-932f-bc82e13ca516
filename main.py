from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import time
import os

def main():
    # 设置ChromeDriver路径
    driver_path = os.path.join(os.getcwd(), "driver", "chromedriver.exe")

    # 创建Chrome服务
    service = Service(driver_path)

    # 创建Chrome选项
    chrome_options = webdriver.ChromeOptions()

    # 创建WebDriver实例
    driver = webdriver.Chrome(service=service, options=chrome_options)

    try:
        # 打开网站
        print("正在打开 https://www.genspark.ai ...")
        driver.get("https://www.genspark.ai")

        print("网站已打开，等待30秒...")
        # 等待30秒
        time.sleep(10)

        print("开始执行reCAPTCHA...")
        try:
            # 先检查grecaptcha是否存在
            grecaptcha_exists = driver.execute_script("return typeof grecaptcha !== 'undefined';")
            if grecaptcha_exists:
                # 执行reCAPTCHA并等待结果
                driver.execute_script("""
                    window.recaptchaToken = null;
                    grecaptcha.ready(function() {
                        grecaptcha.execute('6Leq7KYqAAAAAGdd1NaUBJF9dHTPAKP7DcnaRc66', {action: 'submit'}).then(function(token) {
                            console.log('reCAPTCHA token:', token);
                            window.recaptchaToken = token;
                        });
                    });
                """)

                # 等待token生成（最多等待10秒）
                for i in range(10):
                    time.sleep(1)
                    token = driver.execute_script("return window.recaptchaToken;")
                    if token:
                        print(f"获取到的reCAPTCHA token: {token}")
                        break
                else:
                    print("等待token超时")
            else:
                print("grecaptcha未定义，可能页面还未完全加载reCAPTCHA")
        except Exception as sync_error:
            print(f"同步方式也失败了: {sync_error}")

        print("继续等待...")
        # 继续等待，让用户可以观察结果
        time.sleep(9999)

    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭浏览器
        driver.quit()
        print("浏览器已关闭")

if __name__ == "__main__":
    main()