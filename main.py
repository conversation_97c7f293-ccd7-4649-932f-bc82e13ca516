from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import time
import os
import undetected_chromedriver as uc
from selenium_stealth import stealth

def main():
    # 设置ChromeDriver路径
    driver_path = os.path.join(os.getcwd(), "driver", "chromedriver.exe")
    # driver_path = os.path.join(os.getcwd(), "fingerprint-chromium", "Chromium", "Application","chrome.exe")

    # 创建Chrome服务
    service = Service(driver_path)

    # 创建Chrome选项
    chrome_options = webdriver.ChromeOptions()
    # 启用无头模式
    # chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')

    # 创建WebDriver实例
    # driver = webdriver.Chrome(service=service, options=chrome_options)

    driver=uc.Chrome(driver_executable_path=driver_path, options=chrome_options)

    stealth(driver,
            languages=["en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Google Inc. (NVIDIA)",
            renderer="ANGLE (NVIDIA, NVIDIA GeForce RTX 4060 (0x00002882) Direct3D11 vs_5_0 ps_5_0, D3D11)",
            fix_hairline=True,
            )

    try:
        # 打开网站
        print("正在打开 https://www.genspark.ai ...")
        driver.get("https://auth.augmentcode.com/authorize?response_type=code&code_challenge=HZ4eKcpHg6QxYyeQXScIMuMYkLZiOq55N7SERUxG3hE&client_id=v&state=521000de-4b15-461a-a226-a64116bd22f0&prompt=login")



        print("等待30秒...")
        # 等待30秒
        time.sleep(20)
        print("网站已打开，正在截图...")
        # 截图保存
        screenshot_path = os.path.join(os.getcwd(), "website_screenshot.png")
        driver.save_screenshot(screenshot_path)
        print(f"截图已保存到: {screenshot_path}")


        print("开始执行reCAPTCHA...")
        try:
            # 先检查grecaptcha是否存在
            grecaptcha_exists = driver.execute_script("return typeof grecaptcha !== 'undefined';")
            if grecaptcha_exists:
                # 执行reCAPTCHA并等待结果
                driver.execute_script("""
                    window.recaptchaToken = null;
                    grecaptcha.ready(function() {
                        grecaptcha.execute('6Leq7KYqAAAAAGdd1NaUBJF9dHTPAKP7DcnaRc66', {action: 'submit'}).then(function(token) {
                            console.log('reCAPTCHA token:', token);
                            window.recaptchaToken = token;
                        });
                    });
                """)

                # 等待token生成（最多等待10秒）
                for i in range(10):
                    time.sleep(1)
                    token = driver.execute_script("return window.recaptchaToken;")
                    if token:
                        print(f"获取到的reCAPTCHA token: {token}")
                        break
                else:
                    print("等待token超时")
            else:
                print("grecaptcha未定义，可能页面还未完全加载reCAPTCHA")
        except Exception as sync_error:
            print(f"同步方式也失败了: {sync_error}")

        print("继续等待...")
        # 继续等待，让用户可以观察结果
        time.sleep(9999)

    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭浏览器
        driver.quit()
        print("浏览器已关闭")

if __name__ == "__main__":
    main()